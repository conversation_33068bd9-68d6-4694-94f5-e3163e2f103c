%% ========================================================================
%  三维声波测井物理模型验证脚本
%  功能：验证井内外物理参数设置是否正确
%% ========================================================================

clc; clear;

fprintf('=== 三维声波测井物理模型验证 ===\n\n');

%% 物理参数定义（与主程序保持一致）
% 井内流体参数（钻井液/泥浆）
vp_fluid = 1500;   % 纵波速度 (m/s)
vs_fluid = 0;      % 横波速度 (m/s) - 流体不传横波
rho_fluid = 1000;  % 密度 (kg/m³)

% 井外地层参数（均匀岩石地层）
vp_rock = 4000;    % 纵波速度 (m/s)
vs_rock = 2300;    % 横波速度 (m/s)
rho_rock = 2500;   % 密度 (kg/m³)

%% 计算弹性模量
% 流体弹性模量
G_fluid = rho_fluid * vs_fluid^2;  % 剪切模量
K_fluid = rho_fluid * vp_fluid^2 - 4*G_fluid/3;  % 体积模量

% 岩石弹性模量
G_rock = rho_rock * vs_rock^2;  % 剪切模量
K_rock = rho_rock * vp_rock^2 - 4*G_rock/3;  % 体积模量

%% 显示物理参数
fprintf('=== 井内流体参数（钻井液） ===\n');
fprintf('纵波速度 (Vp)：%d m/s\n', vp_fluid);
fprintf('横波速度 (Vs)：%d m/s\n', vs_fluid);
fprintf('密度 (ρ)：%d kg/m³\n', rho_fluid);
fprintf('体积模量 (K)：%.2e Pa (%.1f GPa)\n', K_fluid, K_fluid/1e9);
fprintf('剪切模量 (G)：%.2e Pa (%.1f GPa)\n', G_fluid, G_fluid/1e9);
fprintf('声阻抗：%.2e kg/(m²·s)\n', rho_fluid * vp_fluid);

fprintf('\n=== 井外地层参数（均匀岩石） ===\n');
fprintf('纵波速度 (Vp)：%d m/s\n', vp_rock);
fprintf('横波速度 (Vs)：%d m/s\n', vs_rock);
fprintf('密度 (ρ)：%d kg/m³\n', rho_rock);
fprintf('体积模量 (K)：%.2e Pa (%.1f GPa)\n', K_rock, K_rock/1e9);
fprintf('剪切模量 (G)：%.2e Pa (%.1f GPa)\n', G_rock, G_rock/1e9);
fprintf('声阻抗：%.2e kg/(m²·s)\n', rho_rock * vp_rock);

%% 物理参数验证
fprintf('\n=== 物理参数合理性检查 ===\n');

% 1. 流体参数检查
if vs_fluid == 0
    fprintf('✓ 流体横波速度为0，符合物理规律\n');
else
    fprintf('❌ 错误：流体横波速度应为0\n');
end

if G_fluid == 0
    fprintf('✓ 流体剪切模量为0，符合物理规律\n');
else
    fprintf('❌ 错误：流体剪切模量应为0\n');
end

% 2. 岩石参数检查
if vp_rock > vs_rock && vs_rock > 0
    fprintf('✓ 岩石中Vp > Vs > 0，符合固体介质规律\n');
else
    fprintf('❌ 错误：岩石波速关系不合理\n');
end

% 3. 密度关系检查
if rho_rock > rho_fluid
    fprintf('✓ 岩石密度 > 流体密度，符合常规情况\n');
else
    fprintf('❌ 警告：密度关系可能不合理\n');
end

% 4. 波速关系检查
if vp_rock > vp_fluid
    fprintf('✓ 岩石纵波速度 > 流体纵波速度，符合常规地层\n');
else
    fprintf('❌ 警告：可能存在低速地层\n');
end

%% 计算重要的物理量
fprintf('\n=== 重要物理量计算 ===\n');

% 声阻抗对比
Z_fluid = rho_fluid * vp_fluid;
Z_rock = rho_rock * vp_rock;
reflection_coeff = (Z_rock - Z_fluid) / (Z_rock + Z_fluid);
fprintf('声阻抗对比：\n');
fprintf('  流体声阻抗：%.2e kg/(m²·s)\n', Z_fluid);
fprintf('  岩石声阻抗：%.2e kg/(m²·s)\n', Z_rock);
fprintf('  反射系数：%.3f\n', reflection_coeff);

% 泊松比计算
poisson_rock = (vp_rock^2 - 2*vs_rock^2) / (2*(vp_rock^2 - vs_rock^2));
fprintf('岩石泊松比：%.3f\n', poisson_rock);

% Vp/Vs比值
vp_vs_ratio = vp_rock / vs_rock;
fprintf('岩石Vp/Vs比值：%.2f\n', vp_vs_ratio);

%% 频率相关参数
f0 = 8000;  % 主频率
fprintf('\n=== 频率相关参数 (f0 = %d Hz) ===\n', f0);

% 波长计算
lambda_fluid = vp_fluid / f0;
lambda_rock_p = vp_rock / f0;
lambda_rock_s = vs_rock / f0;

fprintf('波长：\n');
fprintf('  流体中纵波波长：%.4f m\n', lambda_fluid);
fprintf('  岩石中纵波波长：%.4f m\n', lambda_rock_p);
fprintf('  岩石中横波波长：%.4f m\n', lambda_rock_s);
fprintf('  最小波长：%.4f m (控制网格设计)\n', min([lambda_fluid, lambda_rock_p, lambda_rock_s]));

%% 井筒几何参数
well_radius = 0.1;  % 井筒半径
fprintf('\n=== 井筒几何参数 ===\n');
fprintf('井筒半径：%.1f m\n', well_radius);
fprintf('井筒直径：%.1f m\n', 2*well_radius);

% 井筒与波长的关系
fprintf('井筒尺度与波长关系：\n');
fprintf('  井筒直径/流体波长：%.2f\n', 2*well_radius/lambda_fluid);
fprintf('  井筒直径/岩石纵波波长：%.2f\n', 2*well_radius/lambda_rock_p);

%% 典型声波测井参数对比
fprintf('\n=== 典型声波测井参数对比 ===\n');
fprintf('当前设置与典型值对比：\n');
fprintf('参数                  当前值      典型范围        状态\n');

% 流体Vp检查
if vp_fluid >= 1200 && vp_fluid <= 1800
    status_vp_fluid = '✓';
else
    status_vp_fluid = '⚠';
end
fprintf('流体Vp (m/s)         %4d        1200-1800       %s\n', vp_fluid, status_vp_fluid);

% 流体密度检查
if rho_fluid >= 800 && rho_fluid <= 1200
    status_rho_fluid = '✓';
else
    status_rho_fluid = '⚠';
end
fprintf('流体密度 (kg/m³)     %4d        800-1200        %s\n', rho_fluid, status_rho_fluid);

% 岩石Vp检查
if vp_rock >= 2000 && vp_rock <= 6000
    status_vp_rock = '✓';
else
    status_vp_rock = '⚠';
end
fprintf('岩石Vp (m/s)         %4d        2000-6000       %s\n', vp_rock, status_vp_rock);

% 岩石Vs检查
if vs_rock >= 1000 && vs_rock <= 3500
    status_vs_rock = '✓';
else
    status_vs_rock = '⚠';
end
fprintf('岩石Vs (m/s)         %4d        1000-3500       %s\n', vs_rock, status_vs_rock);

% 岩石密度检查
if rho_rock >= 2000 && rho_rock <= 3000
    status_rho_rock = '✓';
else
    status_rho_rock = '⚠';
end
fprintf('岩石密度 (kg/m³)     %4d        2000-3000       %s\n', rho_rock, status_rho_rock);

% Vp/Vs比值检查
if vp_vs_ratio >= 1.4 && vp_vs_ratio <= 2.0
    status_vpvs = '✓';
else
    status_vpvs = '⚠';
end
fprintf('Vp/Vs比值            %.2f        1.4-2.0         %s\n', vp_vs_ratio, status_vpvs);

% 泊松比检查
if poisson_rock >= 0.1 && poisson_rock <= 0.4
    status_poisson = '✓';
else
    status_poisson = '⚠';
end
fprintf('泊松比               %.3f       0.1-0.4         %s\n', poisson_rock, status_poisson);

%% 总结
fprintf('\n=== 物理模型验证总结 ===\n');
if vs_fluid == 0 && G_fluid == 0 && vp_rock > vs_rock && vs_rock > 0 && ...
   rho_rock > rho_fluid && vp_rock > vp_fluid
    fprintf('✓ 物理模型设置正确，符合声波测井基本要求\n');
else
    fprintf('❌ 物理模型存在问题，请检查参数设置\n');
end

fprintf('\n建议：\n');
if reflection_coeff > 0.3
    fprintf('  - 声阻抗对比较大(%.3f)，界面反射明显，有利于检测\n', reflection_coeff);
elseif reflection_coeff < 0.1
    fprintf('  - 声阻抗对比较小(%.3f)，界面反射较弱\n', reflection_coeff);
else
    fprintf('  - 声阻抗对比适中(%.3f)，适合声波测井\n', reflection_coeff);
end

if 2*well_radius/lambda_fluid < 0.5
    fprintf('  - 井筒直径小于半个波长，可能影响波场特征\n');
end

fprintf('\n验证完成！可以运行主程序进行FDTD模拟。\n');
