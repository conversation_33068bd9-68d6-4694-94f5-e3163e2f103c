%% 测试视觉改进效果
% 测试DAS单独显示和对比图的视觉改进

clear; clc; close all;

% 创建模拟数据进行测试
fprintf('创建模拟数据进行视觉效果测试...\n');

% 模拟参数
num_traces = 10;
num_samples = 1000;
dt = 0.001;  % 1ms采样间隔
time = (0:num_samples-1) * dt;

% 创建模拟DAS数据（包含主要信号和噪声）
das_data = zeros(num_traces, num_samples);
for i = 1:num_traces
    % 主信号：高斯脉冲
    t_center = 0.3 + (i-1) * 0.02;  % 每道延迟20ms
    amplitude = 1.0 * exp(-(i-1)*0.1);  % 振幅随深度衰减
    signal = amplitude * exp(-((time - t_center) / 0.05).^2) .* sin(2*pi*50*time);
    
    % 添加噪声
    noise = 0.1 * randn(1, num_samples);
    
    das_data(i, :) = signal + noise;
end

% 创建模拟检波器数据（稍有不同）
receiver_data = zeros(num_traces, num_samples);
for i = 1:num_traces
    % 主信号：稍有不同的高斯脉冲
    t_center = 0.3 + (i-1) * 0.02 + 0.005;  % 稍有时移
    amplitude = 0.9 * exp(-(i-1)*0.1);  % 稍有振幅差异
    signal = amplitude * exp(-((time - t_center) / 0.048).^2) .* sin(2*pi*52*time);
    
    % 添加噪声
    noise = 0.08 * randn(1, num_samples);
    
    receiver_data(i, :) = signal + noise;
end

% 设置基本参数
count_s = 1;
pos_s = 100;
dz = 0.25;
f0 = 50;
L_RtoR = 4;
len_StoR = 50;
len_RtoR = 4;
first_trace_amplitude_factor = 2.0;
maxt = max(time);
display_time = time;
time_indices = 1:length(time);

% 获取默认配置
config = get_default_config();

% 测试1: DAS单独显示（新的黑线+红色填充效果）
fprintf('测试1: DAS单独显示（黑线+红色填充）...\n');
create_das_display(das_data, count_s, maxt, display_time, time_indices, config, ...
                   pos_s, dz, len_StoR, len_RtoR, first_trace_amplitude_factor);

% 保存图片
saveas(gcf, 'test_das_single_display.png');
fprintf('DAS单独显示图已保存为: test_das_single_display.png\n');

% 测试2: 对比显示（检波器线条加粗效果）
fprintf('测试2: DAS与检波器对比显示（检波器线条加粗）...\n');
create_comparison_display(receiver_data, das_data, count_s, num_traces, maxt, ...
                         display_time, time_indices, config, pos_s, dz, f0, ...
                         L_RtoR, first_trace_amplitude_factor, len_StoR, len_RtoR);

% 保存图片
saveas(gcf, 'test_comparison_display.png');
fprintf('对比显示图已保存为: test_comparison_display.png\n');

fprintf('视觉改进测试完成！\n');
fprintf('请查看生成的图片文件验证以下改进效果：\n');
fprintf('1. DAS单独显示：黑色线条 + 红色填充\n');
fprintf('2. 对比图：检波器线条加粗（1.8线宽）\n');
fprintf('3. 增加的底部留白效果\n');
fprintf('4. 高质量图片输出（400 DPI）\n');

%% 获取默认配置函数
function config = get_default_config()
    % 基本显示配置
    config.trace_spacing = 1.0;
    config.amplitude_scale = 0.8;
    config.font_size = 12;
    config.line_width = 1.0;
    
    % DAS显示配置
    config.das_trace_spacing = 1.0;
    config.das_amplitude_scale = 0.8;
    config.das_channel_start = 1;
    config.das_channel_count = 10;
    config.das_fill_positive = false;
    config.das_fill_color = [1, 0, 0];
    
    % 振幅处理模式
    config.amplitude_mode = 1;
    config.first_trace_original = false;
    
    % 叠加对比模式配置
    config.receiver_line_color = [0, 0, 0];
    config.das_line_color = [1, 0, 0];
    config.receiver_line_width = 1.8;  % 加粗
    config.das_line_width = 1.0;
    
    % 图片质量配置
    config.dpi = 400;
    config.figure_size = [1200, 900];
    
    % DAS单独显示配置
    config.das_single_line_color = [0, 0, 0];     % 黑色线条
    config.das_single_line_width = 1.2;
    config.das_single_fill_color = [1, 0, 0];     % 红色填充
    config.das_single_fill_alpha = 0.4;           % 填充透明度
end
