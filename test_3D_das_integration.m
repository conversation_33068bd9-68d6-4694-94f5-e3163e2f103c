%% ========================================================================
%  三维声波测井DAS集成测试脚本
%  功能：测试DAS系统集成是否正常工作
%% ========================================================================

clc; clear; close all;

fprintf('=== 三维声波测井DAS集成测试 ===\n');
fprintf('测试目标：验证DAS系统是否正确集成到三维FDTD程序中\n\n');

try
    % 运行主程序（可能需要较长时间）
    fprintf('正在运行三维FDTD声波测井模拟...\n');
    fprintf('注意：这可能需要几分钟到几十分钟的时间\n');
    
    tic;
    run('danjizi_yingdiceng.m');
    execution_time = toc;
    
    fprintf('\n=== 测试结果验证 ===\n');
    
    % 验证基本数据结构
    fprintf('1. 基本数据结构验证:\n');
    fprintf('   检波器数据矩阵大小: %d × %d\n', size(receiver_data, 1), size(receiver_data, 2));
    fprintf('   检波器数量: %d\n', num_receivers);
    fprintf('   时间采样点数: %d\n', nt);
    
    % 验证DAS系统
    if exist('enable_das', 'var') && enable_das
        fprintf('   DAS系统: 启用 ✓\n');
        if exist('das_data', 'var')
            fprintf('   DAS数据矩阵大小: %d × %d\n', size(das_data, 1), size(das_data, 2));
            fprintf('   DAS标距点数: %d\n', num_das_points);
            fprintf('   标距长度: %.2f m\n', gauge_length);
            fprintf('   标距重合比例: %.1f%%\n', gauge_overlap*100);
        else
            fprintf('   ❌ 错误：DAS启用但数据缺失\n');
        end
    else
        fprintf('   DAS系统: 禁用\n');
    end
    
    % 验证数据内容
    fprintf('\n2. 数据内容验证:\n');
    receiver_max = max(abs(receiver_data(:)));
    receiver_nonzero = sum(receiver_data(:) ~= 0);
    fprintf('   检波器数据最大值: %.6e\n', receiver_max);
    fprintf('   检波器非零数据点: %d (%.1f%%)\n', receiver_nonzero, receiver_nonzero/numel(receiver_data)*100);
    
    if exist('das_data', 'var')
        das_max = max(abs(das_data(:)));
        das_nonzero = sum(das_data(:) ~= 0);
        fprintf('   DAS数据最大值: %.6e\n', das_max);
        fprintf('   DAS非零数据点: %d (%.1f%%)\n', das_nonzero, das_nonzero/numel(das_data)*100);
    end
    
    % 验证数据合理性
    fprintf('\n3. 数据合理性检查:\n');
    if receiver_max > 0
        fprintf('   ✓ 检波器数据有有效信号\n');
    else
        fprintf('   ❌ 检波器数据全为零\n');
    end
    
    if exist('das_data', 'var') && das_max > 0
        fprintf('   ✓ DAS数据有有效信号\n');
    elseif exist('das_data', 'var')
        fprintf('   ❌ DAS数据全为零\n');
    end
    
    % 验证位置对应关系
    fprintf('\n4. 位置对应关系验证:\n');
    if exist('das_data', 'var') && num_das_points == num_receivers
        fprintf('   ✓ DAS标距点数与检波器数量一致\n');
        fprintf('   ✓ DAS与检波器位置完全对应\n');
    elseif exist('das_data', 'var')
        fprintf('   ❌ DAS标距点数与检波器数量不一致\n');
    end
    
    % 验证文件保存
    fprintf('\n5. 数据保存验证:\n');
    if exist('save_filename', 'var') && exist(save_filename, 'file')
        fprintf('   ✓ 数据文件保存成功: %s\n', save_filename);
        
        % 检查保存的数据
        saved_data = load(save_filename);
        fprintf('   保存的变量数量: %d\n', length(fieldnames(saved_data)));
        
        if isfield(saved_data, 'receiver_data')
            fprintf('   ✓ 检波器数据已保存\n');
        end
        
        if isfield(saved_data, 'das_data')
            fprintf('   ✓ DAS数据已保存\n');
        end
        
        if isfield(saved_data, 'enable_das')
            fprintf('   ✓ DAS配置参数已保存\n');
        end
        
    else
        fprintf('   ❌ 数据文件保存失败\n');
    end
    
    % 性能统计
    fprintf('\n6. 性能统计:\n');
    fprintf('   程序执行时间: %.1f 秒 (%.1f 分钟)\n', execution_time, execution_time/60);
    fprintf('   网格总点数: %d (%.1f M)\n', nx*ny*nz, nx*ny*nz/1e6);
    fprintf('   时间步数: %d\n', nt);
    fprintf('   平均每时间步耗时: %.2f ms\n', execution_time*1000/nt);
    
    % 数值频散评估
    if exist('dispersion_error', 'var')
        fprintf('\n7. 数值频散评估:\n');
        fprintf('   每波长网格点数: %.1f\n', points_per_wavelength);
        fprintf('   CFL数: %.2f\n', cfl);
        fprintf('   估计频散误差: %.2e\n', dispersion_error);
        
        if dispersion_error < 1e-3
            fprintf('   ✓ 频散控制良好\n');
        else
            fprintf('   ⚠ 频散误差较大，建议优化网格参数\n');
        end
    end
    
    fprintf('\n=== 测试成功完成 ===\n');
    fprintf('DAS系统已成功集成到三维声波测井FDTD程序中！\n');
    
    % 提供后续分析建议
    fprintf('\n=== 后续分析建议 ===\n');
    fprintf('1. 运行数据分析脚本:\n');
    fprintf('   analyze_3D_acoustic_data(''%s'')\n', save_filename);
    fprintf('\n2. 手动数据检查:\n');
    fprintf('   load(''%s'')\n', save_filename);
    fprintf('   figure; imagesc(receiver_data''); title(''检波器数据'');\n');
    if exist('das_data', 'var')
        fprintf('   figure; imagesc(das_data''); title(''DAS数据'');\n');
    end
    
    % 自动运行数据分析（可选）
    fprintf('\n是否自动运行数据分析？\n');
    user_input = input('输入 y 自动分析，其他键跳过: ', 's');
    if strcmpi(user_input, 'y')
        fprintf('\n正在运行数据分析...\n');
        analyze_3D_acoustic_data(save_filename);
    end
    
catch ME
    fprintf('\n=== 测试过程中出现错误 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    fprintf('请检查程序代码并修复错误。\n');
    
    % 显示详细错误信息
    fprintf('\n详细错误堆栈:\n');
    for i = 1:length(ME.stack)
        fprintf('  %d. %s (第%d行)\n', i, ME.stack(i).name, ME.stack(i).line);
    end
end

fprintf('\n=== DAS集成测试结束 ===\n');
