%% ========================================================================
%  三维声波测井FDTD程序配置脚本
%  功能：快速调整计算参数，平衡精度和计算效率
%% ========================================================================

clc; clear;

fprintf('=== 三维声波测井FDTD程序参数配置 ===\n\n');

%% 预设配置方案
fprintf('可选配置方案：\n');
fprintf('1. 快速测试    - 低精度，快速验证 (推荐用于程序调试)\n');
fprintf('2. 标准计算    - 中等精度，平衡效率 (推荐日常使用)\n');
fprintf('3. 高精度计算  - 高精度，计算量大 (推荐最终结果)\n');
fprintf('4. 自定义配置  - 手动设置参数\n\n');

choice = input('请选择配置方案 (1-4): ');

switch choice
    case 1
        % 快速测试配置
        config.name = '快速测试';
        config.points_per_wavelength = 15;
        config.cfl = 0.4;
        config.nt = 2000;
        config.enable_das = true;
        
    case 2
        % 标准计算配置
        config.name = '标准计算';
        config.points_per_wavelength = 20;
        config.cfl = 0.3;
        config.nt = 5000;
        config.enable_das = true;
        
    case 3
        % 高精度计算配置
        config.name = '高精度计算';
        config.points_per_wavelength = 25;
        config.cfl = 0.3;
        config.nt = 8000;
        config.enable_das = true;
        
    case 4
        % 自定义配置
        config.name = '自定义';
        fprintf('\n=== 自定义参数设置 ===\n');
        config.points_per_wavelength = input('每波长网格点数 (推荐15-25): ');
        config.cfl = input('CFL数 (推荐0.2-0.4): ');
        config.nt = input('时间步数 (推荐2000-8000): ');
        das_choice = input('启用DAS系统? (1=是, 0=否): ');
        config.enable_das = logical(das_choice);
        
    otherwise
        fprintf('无效选择，使用标准配置\n');
        config.name = '标准计算';
        config.points_per_wavelength = 20;
        config.cfl = 0.3;
        config.nt = 5000;
        config.enable_das = true;
end

%% 计算预估参数
f0 = 8000;  % 固定频率
vp_min = 1500;  % 最小速度
lambda_min = vp_min / f0;
dx = lambda_min / config.points_per_wavelength;
nx = round(1.0/dx) + 1;
ny = nx;
nz = round(6.0/dx) + 1;
total_points = nx * ny * nz;

% 频散误差估计
dispersion_error = (pi * f0 * dx / vp_min)^2 / 24;

% 时间步长估计
vp_max = 4000;
dt = config.cfl * dx / (vp_max * sqrt(3));
record_time = config.nt * dt;

% 内存估计
memory_gb = total_points * 12 * 8 / 1e9;  % 12个场变量，双精度

%% 显示配置结果
fprintf('\n=== 配置结果：%s ===\n', config.name);
fprintf('网格参数：\n');
fprintf('  每波长网格点数：%d\n', config.points_per_wavelength);
fprintf('  网格间距：%.4f m\n', dx);
fprintf('  网格数量：%d × %d × %d = %.1f M\n', nx, ny, nz, total_points/1e6);

fprintf('时间参数：\n');
fprintf('  CFL数：%.2f\n', config.cfl);
fprintf('  时间步数：%d\n', config.nt);
fprintf('  记录时间：%.2f ms\n', record_time * 1000);

fprintf('质量评估：\n');
fprintf('  频散误差：%.2e', dispersion_error);
if dispersion_error < 1e-3
    fprintf(' ✓ 优秀\n');
elseif dispersion_error < 2e-3
    fprintf(' ⚠ 可接受\n');
else
    fprintf(' ❌ 偏大\n');
end

fprintf('资源需求：\n');
fprintf('  估计内存：%.1f GB\n', memory_gb);
fprintf('  DAS系统：%s\n', config.enable_das ? '启用' : '禁用');

% 计算时间估计（粗略）
if total_points < 5e6
    time_estimate = '< 10分钟';
elseif total_points < 10e6
    time_estimate = '10-30分钟';
elseif total_points < 20e6
    time_estimate = '30-60分钟';
else
    time_estimate = '> 1小时';
end
fprintf('  估计计算时间：%s\n', time_estimate);

%% 确认和应用配置
fprintf('\n');
apply_config = input('应用此配置? (1=是, 0=否): ');

if apply_config
    % 修改主程序文件
    fprintf('\n正在应用配置到主程序...\n');
    
    % 读取主程序
    filename = 'danjizi_yingdiceng.m';
    if ~exist(filename, 'file')
        error('找不到主程序文件：%s', filename);
    end
    
    % 备份原文件
    backup_name = sprintf('danjizi_yingdiceng_backup_%s.m', datestr(now, 'yyyymmdd_HHMMSS'));
    copyfile(filename, backup_name);
    fprintf('已备份原文件到：%s\n', backup_name);
    
    % 读取文件内容
    fid = fopen(filename, 'r');
    content = fread(fid, '*char')';
    fclose(fid);
    
    % 修改参数
    content = regexprep(content, 'points_per_wavelength = \d+;', ...
        sprintf('points_per_wavelength = %d;', config.points_per_wavelength));
    content = regexprep(content, 'cfl = [\d\.]+;', ...
        sprintf('cfl = %.2f;', config.cfl));
    content = regexprep(content, 'enable_das = (true|false);', ...
        sprintf('enable_das = %s;', config.enable_das ? 'true' : 'false'));
    
    % 修改计算模式
    if config.nt <= 2500
        mode = 'fast';
    elseif config.nt <= 6000
        mode = 'normal';
    else
        mode = 'high';
    end
    content = regexprep(content, "computation_mode = '[^']*';", ...
        sprintf("computation_mode = '%s';", mode));
    
    % 写回文件
    fid = fopen(filename, 'w');
    fwrite(fid, content);
    fclose(fid);
    
    fprintf('✓ 配置已应用到主程序\n');
    fprintf('✓ 可以运行：run(''%s'')\n', filename);
    
    % 保存配置记录
    config_record.timestamp = datestr(now);
    config_record.config = config;
    config_record.estimated_params.total_points = total_points;
    config_record.estimated_params.dispersion_error = dispersion_error;
    config_record.estimated_params.memory_gb = memory_gb;
    config_record.estimated_params.record_time_ms = record_time * 1000;
    
    save('last_config.mat', 'config_record');
    fprintf('✓ 配置记录已保存到：last_config.mat\n');
    
else
    fprintf('配置未应用。\n');
end

fprintf('\n=== 配置完成 ===\n');

%% 提供运行建议
fprintf('\n=== 运行建议 ===\n');
if total_points > 15e6
    fprintf('⚠ 网格点数较多，建议：\n');
    fprintf('  1. 确保有足够内存 (>%.0f GB)\n', memory_gb);
    fprintf('  2. 关闭其他程序释放内存\n');
    fprintf('  3. 考虑先用快速模式测试\n');
end

if dispersion_error > 1e-3
    fprintf('⚠ 频散误差较大，建议：\n');
    fprintf('  1. 增加网格密度到25-30点/波长\n');
    fprintf('  2. 降低CFL数到0.2-0.25\n');
    fprintf('  3. 仔细检查结果质量\n');
end

fprintf('\n快速命令：\n');
fprintf('  运行模拟：run(''danjizi_yingdiceng.m'')\n');
fprintf('  快速测试：run(''quick_test_das.m'')\n');
fprintf('  数据分析：analyze_3D_acoustic_data()\n');
