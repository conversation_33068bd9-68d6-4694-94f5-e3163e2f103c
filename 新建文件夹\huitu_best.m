% 声波测井标准显示程序（支持DAS和检波器数据）
% 实现类似您展示图片的声波测井波形显示效果

%% ==================== 用户配置区域 ====================
% 0. 绘图模式选择（新增）
plot_mode = 1;  % 绘图模式选择：
                % 1 - 仅绘制检波器波形（原功能）
                % 2 - 仅绘制DAS波形
                % 3 - 绘制DAS和检波器叠加对比波形（黑色=检波器，红色=DAS）

% 1. 数据文件选择
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';
das_data_filename = 'das_simulation_results.mat';  % DAS数据文件（新增）

% 2. 要显示的炮点选择
shot_numbers = [5, 10, 20,30, 35, 40, 45,50, 60, 67]; % 测试炮点

% 3. 检波器显示配置
config.num_receivers_to_show = 21;  % 每炮显示的检波器数量（1-21）
config.receiver_start = 1;          % 起始检波器编号（1-21）
% 示例：显示前10个检波器：num_receivers_to_show=10, receiver_start=1
% 示例：显示中间10个检波器：num_receivers_to_show=10, receiver_start=6
% 示例：显示后10个检波器：num_receivers_to_show=10, receiver_start=12

% 4. DAS显示配置（新增）
config.das_num_channels_to_show = 21;  % 每炮显示的DAS通道数量
config.das_channel_start = 1;          % 起始DAS通道编号
config.das_channel_spacing = 0.015;    % DAS通道间距（米）
% DAS通道选择示例：
% 显示前10个通道：das_num_channels_to_show=10, das_channel_start=1
% 显示中间10个通道：das_num_channels_to_show=10, das_channel_start=6

% 5. 声波测井显示参数配置
config.trace_spacing = 1.5;        % 道间距（垂直偏移）
config.amplitude_scale = 0.8;      % 振幅缩放因子
config.show_first_arrivals = false; % 是否标注首波到达
config.time_range = [0, 0.004];    % 显示时间范围[开始, 结束]秒
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

% 6. DAS显示参数配置（新增）
config.das_trace_spacing = 1.5;    % DAS道间距（垂直偏移）
config.das_amplitude_scale = 0.8;  % DAS振幅缩放因子
config.das_fill_positive = true;   % 是否填充DAS正振幅
config.das_fill_color = [1, 0, 0]; % DAS填充颜色 [R, G, B]（红色，取消紫色）
config.das_fill_alpha = 1.0;       % DAS填充透明度（完全不透明）

% 7. 叠加对比模式配置（新增）
config.receiver_line_color = [0, 0, 0];    % 检波器波形颜色（黑色）
config.das_line_color = [1, 0, 0];         % DAS波形颜色（红色）
config.receiver_line_width = 1.8;          % 检波器线宽（加粗以便在红色背景中显示）
config.das_line_width = 1.0;               % DAS线宽

% 8. 图片质量配置（通用）
config.dpi = 400;                  % 图片分辨率 (400 DPI高清)
config.figure_size = [1200, 900];  % 图形窗口大小 [宽, 高]

% 9. DAS单独显示配置（新增）
config.das_single_line_color = [0, 0, 0];     % DAS单独显示线条颜色（黑色）
config.das_single_line_width = 1.0;           % DAS单独显示线宽（调细以匹配参考图片）
config.das_single_fill_color = [1, 0, 0];     % DAS单独显示填充颜色（红色）
config.das_single_fill_alpha = 0.4;           % DAS单独显示填充透明度

% 9. 振幅显示模式选择（检波器和DAS通用）
% 修改amplitude_mode的值来选择振幅处理模式：
% 1 - 传统归一化（所有道振幅相同，清晰显示）
% 2 - 轻微保留振幅差异
% 3 - 中等保留振幅差异
% 4 - 强烈保留振幅差异
% 5 - 不进行归一化，显示原波形
% 6 - 第一道显示原波形，其余道归一化显示
amplitude_mode = 6;  % 请修改这个数字来选择模式（对检波器和DAS都生效）

% 模式6专用参数：第一道相对于其他道的振幅倍数（参考例子图片的比例）
first_trace_amplitude_factor = 20.0;

% 将amplitude_mode添加到config结构体中，使其在所有函数中可用
config.amplitude_mode = amplitude_mode;

% 根据选择的模式设置参数
switch amplitude_mode
    case 1
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
    case 2
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.3;
    case 3
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.5;
    case 4
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.8;
    case 5
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.no_normalization = true;  % 新增标志：不进行归一化
    case 6
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.first_trace_original = true;  % 新增标志：第一道显示原波形
    otherwise
        warning('无效的amplitude_mode值，使用默认模式1');
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
end

% 为其他模式设置默认值
if amplitude_mode ~= 5
    config.no_normalization = false;
end
if amplitude_mode ~= 6
    config.first_trace_original = false;
end

%% ==================== 数据加载 ====================
% 加载检波器数据文件
if ~exist('data', 'var') || ~exist('X', 'var')
    current_folder = pwd;
    full_data_path = fullfile(current_folder, data_filename);

    try
        load(full_data_path);
        fprintf('成功加载检波器数据文件: %s\n', full_data_path);
    catch ME
        error('检波器数据加载失败: %s', ME.message);
    end
end

% 加载DAS数据文件（如果需要）
das_data = [];
if plot_mode == 2 || plot_mode == 3
    current_folder = pwd;
    full_das_data_path = fullfile(current_folder, das_data_filename);

    try
        das_loaded = load(full_das_data_path);
        if isfield(das_loaded, 'das_data')
            das_data = das_loaded.das_data;
        elseif isfield(das_loaded, 'data')
            das_data = das_loaded.data;
        else
            % 尝试找到第一个数值矩阵
            fields = fieldnames(das_loaded);
            for i = 1:length(fields)
                if isnumeric(das_loaded.(fields{i})) && ndims(das_loaded.(fields{i})) == 2
                    das_data = das_loaded.(fields{i});
                    break;
                end
            end
        end

        if isempty(das_data)
            error('DAS数据文件中未找到有效的数据矩阵');
        end

        fprintf('成功加载DAS数据文件: %s\n', full_das_data_path);
        fprintf('DAS数据矩阵尺寸: %d × %d\n', size(das_data, 1), size(das_data, 2));
    catch ME
        error('DAS数据加载失败: %s', ME.message);
    end
end

% 设置默认参数
if ~exist('num_receivers', 'var')
    if exist('N', 'var')
        num_receivers = N;
    else
        num_receivers = 21;
    end
end

if ~exist('dz', 'var')
    dz = 0.015;
end

if ~exist('pml', 'var')
    pml = 50;
end

if ~exist('nz', 'var')
    nz = 2*pml+1200;
end

if ~exist('len_RtoR', 'var')
    len_RtoR = 10;
end

if ~exist('num_s', 'var')
    num_s = 67;
end

if ~exist('f0', 'var')
    f0 = 10000; % 10 kHz
end

if ~exist('L_RtoR', 'var')
    L_RtoR = 0.15; % 0.15米
end

% 添加缺失的关键变量
if ~exist('len_StoR', 'var')
    if exist('L_StoR', 'var')
        len_StoR = round(L_StoR / dz);  % 从物理距离计算网格点数
    else
        L_StoR = 1.5;  % 默认源距1.5米
        len_StoR = round(L_StoR / dz);
    end
end

% 确保时间相关变量存在
if ~exist('maxt', 'var')
    maxt = 2000;  % 默认时间采样点数
end

if ~exist('dt', 'var')
    if exist('f0', 'var')
        dt = 1/(20*f0);  % 根据频率估算时间步长
    else
        dt = 5e-6;  % 默认时间步长 5微秒
    end
end

%% ==================== 主处理循环 ====================
% 创建Picture文件夹
picture_dir = fullfile(pwd, 'Picture_AcousticLogging');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

% 数据完整性检查
fprintf('\n==================== 数据完整性检查 ====================\n');
fprintf('数据矩阵尺寸: %d × %d\n', size(data, 1), size(data, 2));
fprintf('预期数据尺寸: %d × %d\n', num_s, num_receivers * maxt);
fprintf('关键参数: maxt=%d, dt=%.2e, num_receivers=%d\n', maxt, dt, num_receivers);
fprintf('几何参数: len_StoR=%d, len_RtoR=%d, dz=%.4f\n', len_StoR, len_RtoR, dz);

% 检查数据矩阵尺寸是否匹配
expected_cols = num_receivers * maxt;
if size(data, 2) ~= expected_cols
    warning('数据矩阵列数不匹配！实际: %d, 预期: %d', size(data, 2), expected_cols);
    % 尝试自动修正
    if size(data, 2) > expected_cols
        fprintf('截取前%d列数据\n', expected_cols);
        data = data(:, 1:expected_cols);
    else
        error('数据矩阵列数不足，无法继续处理');
    end
end

% 创建时间向量
time_vec = (1:maxt) * dt;

% 确定时间显示范围
if isempty(config.time_range)
    time_indices = 1:maxt;
    display_time = time_vec;
else
    time_indices = find(time_vec >= config.time_range(1) & time_vec <= config.time_range(2));
    if isempty(time_indices)
        time_indices = 1:maxt;
    end
    display_time = time_vec(time_indices);
end

% 处理每个炮点
for shot_idx = 1:length(shot_numbers)
    count_s = shot_numbers(shot_idx);
    
    % 检查炮点编号是否有效
    fprintf('调试: 检查炮点 %d，数据矩阵大小: %d × %d\n', count_s, size(data, 1), size(data, 2));
    if count_s > size(data, 1)
        fprintf('警告: 炮点 %d 超出数据范围，跳过\n', count_s);
        continue;
    end
    
    % 计算激发点位置
    pos_s = nz - 3*pml - (count_s-1) * len_RtoR;
    
    fprintf('\n============ 第%d个炮点信息 ============\n', count_s);
    fprintf('激发点位置（网格坐标）: %d\n', pos_s);
    fprintf('激发点深度（米）: %.2f\n', pos_s * dz);
    
    % 根据绘图模式选择相应的绘图函数
    switch plot_mode
        case 1
            % 仅绘制检波器波形（原功能）
            create_acoustic_logging_display(data, count_s, num_receivers, maxt, ...
                                           display_time, time_indices, config, ...
                                           pos_s, dz, f0, L_RtoR, first_trace_amplitude_factor, ...
                                           len_StoR, len_RtoR);
            fig_filename = fullfile(picture_dir, sprintf('检波器_炮点_%d.png', count_s));

        case 2
            % 仅绘制DAS波形
            create_das_display(das_data, count_s, maxt, ...
                              display_time, time_indices, config, ...
                              pos_s, dz, len_StoR, len_RtoR, first_trace_amplitude_factor);
            fig_filename = fullfile(picture_dir, sprintf('DAS_炮点_%d.png', count_s));

        case 3
            % 绘制DAS和检波器叠加对比波形
            create_comparison_display(data, das_data, count_s, num_receivers, maxt, ...
                                     display_time, time_indices, config, ...
                                     pos_s, dz, f0, L_RtoR, first_trace_amplitude_factor, ...
                                     len_StoR, len_RtoR);
            fig_filename = fullfile(picture_dir, sprintf('对比_炮点_%d.png', count_s));

        otherwise
            error('无效的绘图模式！请选择1（检波器）、2（DAS）或3（对比）');
    end

    % 保存高清图形

    % 设置高分辨率保存参数
    set(gcf, 'PaperPositionMode', 'auto');
    set(gcf, 'PaperUnits', 'inches');
    set(gcf, 'PaperSize', [12, 9]);  % 设置纸张大小

    % 使用print函数保存高质量图片
    print(gcf, fig_filename, '-dpng', sprintf('-r%d', config.dpi));
    fprintf('保存高清图片 (%d DPI): %s\n', config.dpi, fig_filename);
    
    % 关闭图形窗口
    close(gcf);
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);
fprintf('程序执行完成！共处理了 %d 个炮点\n', length(shot_numbers));

%% ==================== 辅助函数 ====================
function create_acoustic_logging_display(data, count_s, num_receivers, maxt, ...
                                        display_time, time_indices, config, ...
                                        pos_s, dz, ~, ~, first_trace_amplitude_factor, ...
                                        len_StoR, len_RtoR)
    % 创建声波测井标准显示
    
    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;
    
    % 计算归一化因子
    max_amplitude = 0;
    for j = 1:num_receivers
        temp_data = data(count_s, (j-1)*maxt+1:j*maxt);
        max_amplitude = max(max_amplitude, max(abs(temp_data)));
    end
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end
    
    % 计算实际显示的检波器范围
    receiver_start = config.receiver_start;
    receiver_end = min(receiver_start + config.num_receivers_to_show - 1, num_receivers);
    actual_receivers = receiver_end - receiver_start + 1;

    % 绘制每道数据
    first_arrivals = zeros(1, actual_receivers);  % 预分配数组
    first_arrival_count = 0;  % 计数器
    for i = receiver_start:receiver_end
        % 道偏移位置（从1开始连续编号）
        display_index = i - receiver_start + 1;

        % 为模式6的第一道留出更多空间（参考例子图片的空间比例）
        if config.first_trace_original && display_index == 1
            % 第一道：使用更大的间距，为大振幅留出足够空间
            trace_offset = display_index * config.trace_spacing * 6.0;
        elseif config.first_trace_original && display_index > 1
            % 其他道：在第一道的基础上正常间距
            trace_offset = config.trace_spacing * 6.0 + (display_index - 1) * config.trace_spacing;
        else
            % 其他模式：正常间距
            trace_offset = display_index * config.trace_spacing;
        end

        % 提取数据 - 添加边界检查
        data_start_idx = (i-1)*maxt+1;
        data_end_idx = i*maxt;

        % 检查数据索引是否超出范围
        if data_end_idx > size(data, 2)
            error('数据索引超出范围！检波器%d需要索引%d-%d，但数据只有%d列', ...
                  i, data_start_idx, data_end_idx, size(data, 2));
        end

        if count_s > size(data, 1)
            error('炮点编号%d超出数据范围！数据只有%d行', count_s, size(data, 1));
        end

        orig_data = data(count_s, data_start_idx:data_end_idx);

        % 检查时间索引是否超出范围
        if max(time_indices) > length(orig_data)
            warning('时间索引超出范围，使用全部时间数据');
            time_indices = 1:length(orig_data);
            display_time = time_vec(time_indices);
        end

        plot_data = orig_data(time_indices);
        
        % 智能振幅处理
        if config.no_normalization
            % 模式5：不进行归一化，显示原波形
            norm_data = plot_data * config.amplitude_scale;
        elseif config.first_trace_original
            % 模式6：第一道显示原波形，其余道归一化显示
            if i == receiver_start
                % 第一道：显示原波形，但进行适当缩放以避免过大
                % 计算第一道的最大振幅
                first_trace_max = max(abs(plot_data));
                if first_trace_max > 0
                    % 使用可调节的缩放因子，既保留振幅特征又不至于太大
                    % 让第一道的最大振幅为归一化道的指定倍数
                    first_trace_scale = config.amplitude_scale * first_trace_amplitude_factor;
                    norm_data = plot_data / first_trace_max * first_trace_scale;
                else
                    norm_data = plot_data * config.amplitude_scale;
                end
            else
                % 其余道：传统归一化
                norm_data = plot_data / max_amplitude * config.amplitude_scale;
            end
        elseif config.preserve_amplitude
            % 计算该道的最大振幅
            trace_max = max(abs(plot_data));

            if trace_max > 0
                % 混合归一化策略：部分保留振幅差异，部分归一化显示
                % 传统归一化部分
                normalized_part = plot_data / max_amplitude * config.amplitude_scale;
                % 保留振幅部分
                preserved_part = plot_data / trace_max * config.amplitude_scale * (trace_max / max_amplitude);
                % 按比例混合
                norm_data = config.amplitude_balance * preserved_part + ...
                           (1 - config.amplitude_balance) * normalized_part;
            else
                norm_data = plot_data * config.amplitude_scale;
            end
        else
            % 传统归一化
            norm_data = plot_data / max_amplitude * config.amplitude_scale;
        end
        
        % 绘制基线
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
        
        % 绘制波形
        wave_y = norm_data + trace_offset;
        plot(display_time, wave_y, 'k-', 'LineWidth', 1.2);
        
        % 填充正振幅（如果启用）
        if config.fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                % 找到连续的正振幅区间
                positive_idx = positive_idx(:); % 确保是列向量
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                % 对每个连续区间分别填充
                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    % 提取该区间的数据
                    seg_time = display_time(seg_start:seg_end);
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    % 填充该区间
                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, config.fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
                end
            end
        end
        
        % 检测首波到达（如果启用）
        if config.show_first_arrivals
            first_arrival = detect_first_arrival_simple(plot_data, display_time);
            if first_arrival > 0
                plot(first_arrival, trace_offset, 'ro', 'MarkerSize', 6, ...
                     'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
                first_arrival_count = first_arrival_count + 1;
                first_arrivals(first_arrival_count) = first_arrival;
            end
        end
        
        % 不在这里添加道标签，因为Y轴已经有标签了
    end
    
    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');
    
    title_str = sprintf('第%d炮声波测井数据 (深度: %.2fm)', count_s, pos_s * dz);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');
    
    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);
    
    % Y轴设置 - 左侧显示物理深度，右侧显示检波器编号（支持模式6第一道特殊间距）
    if config.first_trace_original
        % 模式6：第一道使用特殊间距
        tick_positions = config.trace_spacing * 6.0;  % 第一道位置
        for j = 2:actual_receivers
            tick_positions(j) = config.trace_spacing * 6.0 + (j-1) * config.trace_spacing;
        end
        yticks(tick_positions);
    else
        % 其他模式：正常间距
        yticks(config.trace_spacing:config.trace_spacing:actual_receivers*config.trace_spacing);
    end

    % 计算检波器的真实物理深度位置
    depth_labels = cell(1, actual_receivers);

    for i = 1:actual_receivers
        actual_receiver_num = receiver_start + i - 1;

        % 使用正确的检波器位置计算公式
        % 检波器在震源上方，使用实际的几何参数
        receiver_grid_pos = pos_s - len_StoR - (actual_receiver_num-1) * len_RtoR;
        receiver_depth = receiver_grid_pos * dz;

        depth_labels{i} = sprintf('%.2f', receiver_depth);
    end

    % 设置左侧Y轴标签（深度）
    yticklabels(depth_labels);

    % 范围设置
    xlim([display_time(1), display_time(end)]);

    % 为模式6的第一道调整Y轴范围，确保大振幅波形完全显示
    if config.first_trace_original
        % 第一道需要更多底部空间来显示负振幅，增加到5倍空间
        y_bottom = -5.0 * config.trace_spacing;  % 为第一道的负振幅留出更多空间
        y_top = config.trace_spacing * 6.0 + (actual_receivers-1+0.5) * config.trace_spacing;
        ylim([y_bottom, y_top]);
    else
        % 其他模式使用原来的范围
        ylim([0.5*config.trace_spacing, (actual_receivers+0.5)*config.trace_spacing]);
    end
    
    % 不添加左上角信息框
end

function first_arrival_time = detect_first_arrival_simple(data, time_vec)
    % 简单的首波到达检测
    abs_data = abs(data);
    threshold = 0.05 * max(abs_data);
    first_idx = find(abs_data > threshold, 1, 'first');

    if ~isempty(first_idx) && first_idx > 1
        first_arrival_time = time_vec(first_idx);
    else
        first_arrival_time = -1;
    end
end

%% ==================== DAS绘图函数 ====================
function create_das_display(das_data, count_s, maxt, display_time, time_indices, config, ...
                            pos_s, dz, len_StoR, len_RtoR, first_trace_amplitude_factor)
    % 创建DAS数据显示

    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;

    % 检查DAS数据维度和格式
    if size(das_data, 1) < count_s
        error('DAS数据炮点数不足！需要炮点%d，但只有%d炮', count_s, size(das_data, 1));
    end

    % 计算DAS通道数（假设每个时间采样点对应一个通道）
    das_channels = size(das_data, 2) / maxt;
    if mod(size(das_data, 2), maxt) ~= 0
        error('DAS数据格式不匹配！列数应该是时间采样点数的整数倍');
    end

    % 计算归一化因子
    max_amplitude = 0;
    channel_end = min(config.das_channel_start + config.das_num_channels_to_show - 1, das_channels);
    for j = config.das_channel_start:channel_end
        temp_data = das_data(count_s, (j-1)*maxt+1:j*maxt);
        max_amplitude = max(max_amplitude, max(abs(temp_data)));
    end
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end

    % 绘制每个DAS通道
    actual_channels = channel_end - config.das_channel_start + 1;
    for i = config.das_channel_start:channel_end
        % 通道偏移位置
        display_index = i - config.das_channel_start + 1;

        % 为模式6的第一道留出更多空间
        if config.first_trace_original && display_index == 1
            % 第一道：使用更大的间距，为大振幅留出足够空间
            trace_offset = display_index * config.das_trace_spacing * 6.0;
        elseif config.first_trace_original && display_index > 1
            % 其他道：在第一道的基础上正常间距
            trace_offset = config.das_trace_spacing * 6.0 + (display_index - 1) * config.das_trace_spacing;
        else
            % 其他模式：正常间距
            trace_offset = display_index * config.das_trace_spacing;
        end

        % 提取DAS数据
        data_start_idx = (i-1)*maxt+1;
        data_end_idx = i*maxt;

        if data_end_idx > size(das_data, 2)
            error('DAS数据索引超出范围！通道%d需要索引%d-%d，但数据只有%d列', ...
                  i, data_start_idx, data_end_idx, size(das_data, 2));
        end

        orig_data = das_data(count_s, data_start_idx:data_end_idx);
        plot_data = orig_data(time_indices);

        % 应用通用振幅处理模式
        norm_data = apply_amplitude_mode(plot_data, display_index, config.amplitude_mode, first_trace_amplitude_factor);

        % 应用DAS特定的振幅缩放
        norm_data = norm_data * config.das_amplitude_scale;

        % 绘制基线（更清晰的灰色线条）
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.8, 'Color', [0.6, 0.6, 0.6]);

        % 绘制DAS波形（完整黑色线条 + 只填充正振幅）
        wave_y = norm_data + trace_offset;

        % 先绘制完整的黑色线条（包括正值和负值）
        plot(display_time, wave_y, 'Color', config.das_single_line_color, ...
             'LineWidth', config.das_single_line_width);

        % 填充正振幅（如果启用）
        if config.das_fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                positive_idx = positive_idx(:);
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    seg_time = display_time(seg_start:seg_end);
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, config.das_fill_color, 'EdgeColor', 'none', 'FaceAlpha', config.das_fill_alpha);
                end
            end
        end
    end

    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');

    title_str = sprintf('第%d炮DAS数据 (深度: %.2fm)', count_s, pos_s * dz);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');

    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);

    % Y轴设置 - 显示DAS通道对应的深度（支持模式6第一道特殊间距）
    if config.first_trace_original
        % 模式6：第一道使用特殊间距
        tick_positions = config.das_trace_spacing * 6.0;  % 第一道位置
        for j = 2:actual_channels
            tick_positions(j) = config.das_trace_spacing * 6.0 + (j-1) * config.das_trace_spacing;
        end
        yticks(tick_positions);
    else
        % 其他模式：正常间距
        yticks(config.das_trace_spacing:config.das_trace_spacing:actual_channels*config.das_trace_spacing);
    end

    % 计算DAS通道的深度标签
    depth_labels = cell(1, actual_channels);
    for i = 1:actual_channels
        actual_channel_num = config.das_channel_start + i - 1;
        % DAS通道深度计算（使用与检波器完全相同的几何关系）
        channel_grid_pos = pos_s - len_StoR - (actual_channel_num-1) * len_RtoR;
        channel_depth = channel_grid_pos * dz;
        depth_labels{i} = sprintf('%.2f', channel_depth);
    end

    yticklabels(depth_labels);

    % 范围设置（增加底部留白，支持模式6第一道）
    xlim([display_time(1), display_time(end)]);

    % 为模式6的第一道调整Y轴范围
    if config.first_trace_original
        % 第一道需要更多底部空间来显示负振幅
        y_bottom = -5.0 * config.das_trace_spacing;
        y_top = config.das_trace_spacing * 6.0 + (actual_channels-1+0.5) * config.das_trace_spacing;
        ylim([y_bottom, y_top]);
    else
        ylim([0, (actual_channels+0.5)*config.das_trace_spacing]);  % 底部从0开始，增加留白
    end
end

%% ==================== DAS与检波器对比绘图函数 ====================
function create_comparison_display(data, das_data, count_s, num_receivers, maxt, ...
                                  display_time, time_indices, config, ...
                                  pos_s, dz, f0, L_RtoR, first_trace_amplitude_factor, ...
                                  len_StoR, len_RtoR)
    % 创建DAS与检波器叠加对比显示

    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;

    % 检查数据完整性
    if size(data, 1) < count_s
        error('检波器数据炮点数不足！需要炮点%d，但只有%d炮', count_s, size(data, 1));
    end
    if size(das_data, 1) < count_s
        error('DAS数据炮点数不足！需要炮点%d，但只有%d炮', count_s, size(das_data, 1));
    end

    % 计算DAS通道数
    das_channels = size(das_data, 2) / maxt;
    if mod(size(das_data, 2), maxt) ~= 0
        error('DAS数据格式不匹配！列数应该是时间采样点数的整数倍');
    end

    % 计算检波器归一化因子
    max_receiver_amplitude = 0;
    receiver_end = min(config.receiver_start + config.num_receivers_to_show - 1, num_receivers);
    for j = config.receiver_start:receiver_end
        temp_data = data(count_s, (j-1)*maxt+1:j*maxt);
        max_receiver_amplitude = max(max_receiver_amplitude, max(abs(temp_data)));
    end
    if max_receiver_amplitude < 1e-10
        max_receiver_amplitude = 1;
    end

    % 计算DAS归一化因子
    max_das_amplitude = 0;
    das_channel_end = min(config.das_channel_start + config.das_num_channels_to_show - 1, das_channels);
    for j = config.das_channel_start:das_channel_end
        temp_data = das_data(count_s, (j-1)*maxt+1:j*maxt);
        max_das_amplitude = max(max_das_amplitude, max(abs(temp_data)));
    end
    if max_das_amplitude < 1e-10
        max_das_amplitude = 1;
    end

    % 确定显示的道数（取两者的最小值）
    actual_receivers = receiver_end - config.receiver_start + 1;
    actual_das_channels = das_channel_end - config.das_channel_start + 1;
    num_traces_to_show = min(actual_receivers, actual_das_channels);

    % 绘制每道的检波器和DAS数据对比
    for i = 1:num_traces_to_show
        % 道偏移位置（支持模式6第一道特殊间距）
        if config.first_trace_original && i == 1
            % 第一道：使用更大的间距，为大振幅留出足够空间
            trace_offset = i * config.trace_spacing * 6.0;
        elseif config.first_trace_original && i > 1
            % 其他道：在第一道的基础上正常间距
            trace_offset = config.trace_spacing * 6.0 + (i - 1) * config.trace_spacing;
        else
            % 其他模式：正常间距
            trace_offset = i * config.trace_spacing;
        end

        % === 绘制检波器数据（黑色） ===
        receiver_idx = config.receiver_start + i - 1;
        data_start_idx = (receiver_idx-1)*maxt+1;
        data_end_idx = receiver_idx*maxt;

        if data_end_idx <= size(data, 2)
            orig_receiver_data = data(count_s, data_start_idx:data_end_idx);
            plot_receiver_data = orig_receiver_data(time_indices);

            % 应用通用振幅处理模式（检波器）
            norm_receiver_data = apply_amplitude_mode(plot_receiver_data, i, config.amplitude_mode, first_trace_amplitude_factor);

            % 应用检波器特定的振幅缩放
            norm_receiver_data = norm_receiver_data * config.amplitude_scale;

            % 绘制检波器波形（黑色）
            receiver_wave_y = norm_receiver_data + trace_offset;
            plot(display_time, receiver_wave_y, 'Color', config.receiver_line_color, ...
                 'LineWidth', config.receiver_line_width);
        end

        % === 绘制DAS数据（红色） ===
        das_idx = config.das_channel_start + i - 1;
        das_start_idx = (das_idx-1)*maxt+1;
        das_end_idx = das_idx*maxt;

        if das_end_idx <= size(das_data, 2)
            orig_das_data = das_data(count_s, das_start_idx:das_end_idx);
            plot_das_data = orig_das_data(time_indices);

            % 应用通用振幅处理模式（DAS）
            norm_das_data = apply_amplitude_mode(plot_das_data, i, config.amplitude_mode, first_trace_amplitude_factor);

            % 应用DAS特定的振幅缩放
            norm_das_data = norm_das_data * config.das_amplitude_scale;

            % 绘制DAS波形（红色）
            das_wave_y = norm_das_data + trace_offset;
            plot(display_time, das_wave_y, 'Color', config.das_line_color, ...
                 'LineWidth', config.das_line_width);
        end

        % 绘制基线
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
    end

    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');

    title_str = sprintf('第%d炮检波器与DAS对比 (深度: %.2fm)', count_s, pos_s * dz);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');

    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);

    % Y轴设置（支持模式6第一道特殊间距）
    if config.first_trace_original
        % 模式6：第一道使用特殊间距
        tick_positions = config.trace_spacing * 6.0;  % 第一道位置
        for j = 2:num_traces_to_show
            tick_positions(j) = config.trace_spacing * 6.0 + (j-1) * config.trace_spacing;
        end
        yticks(tick_positions);
    else
        % 其他模式：正常间距
        yticks(config.trace_spacing:config.trace_spacing:num_traces_to_show*config.trace_spacing);
    end

    % 计算深度标签（基于检波器位置）
    depth_labels = cell(1, num_traces_to_show);
    for i = 1:num_traces_to_show
        actual_receiver_num = config.receiver_start + i - 1;
        receiver_grid_pos = pos_s - len_StoR - (actual_receiver_num-1) * len_RtoR;
        receiver_depth = receiver_grid_pos * dz;
        depth_labels{i} = sprintf('%.2f', receiver_depth);
    end

    yticklabels(depth_labels);

    % 范围设置
    xlim([display_time(1), display_time(end)]);

    % 为模式6的第一道调整Y轴范围（增加留白）
    if config.first_trace_original
        y_bottom = -5.0 * config.trace_spacing;
        y_top = config.trace_spacing * 6.0 + (num_traces_to_show-1+0.5) * config.trace_spacing;
        ylim([y_bottom, y_top]);
    else
        ylim([0, (num_traces_to_show+0.5)*config.trace_spacing]);  % 底部从0开始，增加留白
    end

    % 添加图例
    legend({'检波器 (黑色)', 'DAS (红色)'}, 'Location', 'northeast', 'FontSize', 10);
end

%% ==================== 通用振幅处理函数 ====================
function processed_data = apply_amplitude_mode(trace_data, trace_index, amplitude_mode, first_trace_amplitude_factor)
    % 应用通用的振幅处理模式
    % 输入:
    %   trace_data - 原始道数据
    %   trace_index - 道索引（从1开始）
    %   amplitude_mode - 振幅模式（1-6）
    %   first_trace_amplitude_factor - 第一道振幅倍数（仅模式6使用）
    % 输出:
    %   processed_data - 处理后的道数据

    switch amplitude_mode
        case 1
            % 传统归一化（所有道振幅相同，清晰显示）
            if max(abs(trace_data)) > 0
                processed_data = trace_data / max(abs(trace_data));
            else
                processed_data = trace_data;
            end

        case 2
            % 轻微保留振幅差异
            global_max = max(abs(trace_data));
            if global_max > 0
                local_max = max(abs(trace_data));
                balance_factor = 0.3;
                norm_factor = balance_factor * global_max + (1 - balance_factor) * local_max;
                processed_data = trace_data / norm_factor;
            else
                processed_data = trace_data;
            end

        case 3
            % 中等保留振幅差异
            global_max = max(abs(trace_data));
            if global_max > 0
                local_max = max(abs(trace_data));
                balance_factor = 0.5;
                norm_factor = balance_factor * global_max + (1 - balance_factor) * local_max;
                processed_data = trace_data / norm_factor;
            else
                processed_data = trace_data;
            end

        case 4
            % 强烈保留振幅差异
            global_max = max(abs(trace_data));
            if global_max > 0
                local_max = max(abs(trace_data));
                balance_factor = 0.8;
                norm_factor = balance_factor * global_max + (1 - balance_factor) * local_max;
                processed_data = trace_data / norm_factor;
            else
                processed_data = trace_data;
            end

        case 5
            % 不进行归一化，显示原波形
            processed_data = trace_data;

        case 6
            % 第一道显示原波形，其余道归一化显示
            if trace_index == 1
                % 第一道特殊处理：保持原始振幅但放大显示
                if max(abs(trace_data)) > 0
                    processed_data = trace_data / max(abs(trace_data)) * first_trace_amplitude_factor;
                else
                    processed_data = trace_data * first_trace_amplitude_factor;
                end
            else
                % 其他道正常归一化
                if max(abs(trace_data)) > 0
                    processed_data = trace_data / max(abs(trace_data));
                else
                    processed_data = trace_data;
                end
            end

        otherwise
            % 默认使用模式1
            if max(abs(trace_data)) > 0
                processed_data = trace_data / max(abs(trace_data));
            else
                processed_data = trace_data;
            end
    end
end
