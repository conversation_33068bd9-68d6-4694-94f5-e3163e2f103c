%% 简化的视觉效果测试
clear; clc; close all;

fprintf('创建简化的视觉效果测试...\n');

% 创建模拟数据
num_traces = 5;
num_samples = 500;
dt = 0.001;
time = (0:num_samples-1) * dt;

% 模拟DAS数据
das_data = zeros(num_traces, num_samples);
for i = 1:num_traces
    t_center = 0.2 + (i-1) * 0.03;
    amplitude = 1.0 * exp(-(i-1)*0.2);
    signal = amplitude * exp(-((time - t_center) / 0.04).^2) .* sin(2*pi*40*time);
    noise = 0.05 * randn(1, num_samples);
    das_data(i, :) = signal + noise;
end

% 模拟检波器数据
receiver_data = das_data * 0.8 + 0.03 * randn(size(das_data));

% 配置参数
config.das_single_line_color = [0, 0, 0];     % 黑色线条
config.das_single_line_width = 1.2;
config.das_single_fill_color = [1, 0, 0];     % 红色填充
config.das_single_fill_alpha = 0.4;
config.receiver_line_width = 1.8;             % 检波器线条加粗
config.das_line_width = 1.0;
config.receiver_line_color = [0, 0, 0];
config.das_line_color = [1, 0, 0];
config.trace_spacing = 1.0;
config.das_trace_spacing = 1.0;

% 测试1: DAS单独显示效果
fprintf('测试DAS单独显示效果...\n');
figure('Position', [100, 100, 1200, 800], 'Color', 'w');
hold on;

for i = 1:num_traces
    trace_offset = i * config.das_trace_spacing;
    norm_data = das_data(i, :) / max(abs(das_data(i, :))) * 0.4;
    wave_y = norm_data + trace_offset;
    
    % 绘制基线
    plot([time(1), time(end)], [trace_offset, trace_offset], ...
         'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
    
    % 绘制填充区域（红色）
    baseline_y = ones(size(time)) * trace_offset;
    fill([time, fliplr(time)], [wave_y, fliplr(baseline_y)], ...
         config.das_single_fill_color, 'FaceAlpha', config.das_single_fill_alpha, ...
         'EdgeColor', 'none');
    
    % 绘制黑色线条
    plot(time, wave_y, 'Color', config.das_single_line_color, ...
         'LineWidth', config.das_single_line_width);
end

xlim([time(1), time(end)]);
ylim([0, (num_traces+0.5)*config.das_trace_spacing]);  % 增加底部留白
xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');
title('DAS数据显示 - 黑线+红色填充效果', 'FontSize', 16, 'FontWeight', 'bold');
grid on;

% 保存图片
print('das_single_improved.png', '-dpng', '-r400');
fprintf('DAS单独显示图已保存: das_single_improved.png\n');

% 测试2: 对比显示效果
fprintf('测试对比显示效果...\n');
figure('Position', [200, 200, 1200, 800], 'Color', 'w');
hold on;

for i = 1:num_traces
    trace_offset = i * config.trace_spacing;
    
    % 检波器数据（黑色，加粗）
    receiver_norm = receiver_data(i, :) / max(abs(receiver_data(i, :))) * 0.4;
    receiver_y = receiver_norm + trace_offset;
    plot(time, receiver_y, 'Color', config.receiver_line_color, ...
         'LineWidth', config.receiver_line_width);
    
    % DAS数据（红色）
    das_norm = das_data(i, :) / max(abs(das_data(i, :))) * 0.4;
    das_y = das_norm + trace_offset;
    plot(time, das_y, 'Color', config.das_line_color, ...
         'LineWidth', config.das_line_width);
    
    % 绘制基线
    plot([time(1), time(end)], [trace_offset, trace_offset], ...
         'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
end

xlim([time(1), time(end)]);
ylim([0, (num_traces+0.5)*config.trace_spacing]);  % 增加底部留白
xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');
title('检波器与DAS对比显示 - 检波器线条加粗效果', 'FontSize', 16, 'FontWeight', 'bold');
legend({'检波器 (黑色)', 'DAS (红色)'}, 'Location', 'northeast', 'FontSize', 12);
grid on;

% 保存图片
print('comparison_improved.png', '-dpng', '-r400');
fprintf('对比显示图已保存: comparison_improved.png\n');

fprintf('\n视觉改进测试完成！\n');
fprintf('改进效果：\n');
fprintf('1. DAS单独显示：黑色线条 + 红色半透明填充\n');
fprintf('2. 对比图：检波器线条加粗至1.8线宽\n');
fprintf('3. 两种图都增加了底部留白（从0开始）\n');
fprintf('4. 高质量图片输出（400 DPI）\n');
