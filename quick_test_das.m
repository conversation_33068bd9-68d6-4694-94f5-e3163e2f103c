%% ========================================================================
%  快速DAS集成测试（小规模网格）
%  功能：使用小网格快速验证DAS系统集成
%% ========================================================================

clc; clear; close all;

fprintf('=== 快速DAS集成测试 ===\n');
fprintf('使用小规模网格快速验证DAS系统...\n\n');

% 临时修改网格参数进行快速测试
original_file = 'danjizi_yingdiceng.m';
backup_file = 'danjizi_yingdiceng_backup.m';
test_file = 'danjizi_yingdiceng_test.m';

try
    % 备份原文件
    copyfile(original_file, backup_file);
    fprintf('已备份原文件到: %s\n', backup_file);
    
    % 读取原文件内容
    fid = fopen(original_file, 'r');
    content = fread(fid, '*char')';
    fclose(fid);
    
    % 修改参数为小规模测试
    % 修改网格密度
    content = regexprep(content, 'points_per_wavelength = 15;', 'points_per_wavelength = 8;');
    % 修改时间步数
    content = regexprep(content, 'nt = 8000;', 'nt = 1000;');
    % 修改物理尺寸
    content = regexprep(content, 'Lx = 1.0;', 'Lx = 0.5;');
    content = regexprep(content, 'Ly = 1.0;', 'Ly = 0.5;');
    content = regexprep(content, 'Lz = 6.0;', 'Lz = 3.0;');
    
    % 写入测试文件
    fid = fopen(test_file, 'w');
    fwrite(fid, content);
    fclose(fid);
    
    fprintf('已创建测试文件: %s\n', test_file);
    fprintf('测试参数: 小网格, 1000时间步, 减小物理尺寸\n\n');
    
    % 运行测试
    fprintf('正在运行快速测试...\n');
    tic;
    run(test_file);
    test_time = toc;
    
    fprintf('\n=== 快速测试结果 ===\n');
    fprintf('测试执行时间: %.1f 秒\n', test_time);
    fprintf('网格规模: %d × %d × %d = %d 点\n', nx, ny, nz, nx*ny*nz);
    
    % 验证DAS集成
    if exist('enable_das', 'var') && enable_das && exist('das_data', 'var')
        fprintf('✓ DAS系统集成成功\n');
        fprintf('  检波器数据: %d × %d\n', size(receiver_data));
        fprintf('  DAS数据: %d × %d\n', size(das_data));
        fprintf('  检波器最大值: %.2e\n', max(abs(receiver_data(:))));
        fprintf('  DAS最大值: %.2e\n', max(abs(das_data(:))));
        
        % 简单可视化
        figure('Name', '快速测试结果', 'Position', [100 100 1000 600]);
        
        subplot(2, 2, 1);
        imagesc(receiver_data');
        title('检波器数据');
        xlabel('时间步');
        ylabel('检波器道数');
        colorbar;
        
        subplot(2, 2, 2);
        imagesc(das_data');
        title('DAS数据');
        xlabel('时间步');
        ylabel('DAS标距点');
        colorbar;
        
        subplot(2, 2, 3);
        mid_receiver = ceil(num_receivers/2);
        plot(receiver_data(:, mid_receiver), 'b-', 'LineWidth', 1.5);
        title(sprintf('第%d个检波器波形', mid_receiver));
        xlabel('时间步');
        ylabel('压力');
        grid on;
        
        subplot(2, 2, 4);
        plot(das_data(:, mid_receiver), 'r-', 'LineWidth', 1.5);
        title(sprintf('第%d个DAS标距点波形', mid_receiver));
        xlabel('时间步');
        ylabel('应变率');
        grid on;
        
        fprintf('✓ 可视化图表已生成\n');
        
    else
        fprintf('❌ DAS系统集成失败\n');
    end
    
    % 清理测试文件
    if exist(test_file, 'file')
        delete(test_file);
        fprintf('已清理测试文件\n');
    end
    
    fprintf('\n=== 快速测试完成 ===\n');
    fprintf('DAS集成验证: %s\n', exist('das_data', 'var') && enable_das ? '成功' : '失败');
    
catch ME
    fprintf('\n=== 测试出现错误 ===\n');
    fprintf('错误: %s\n', ME.message);
    
    % 清理测试文件
    if exist(test_file, 'file')
        delete(test_file);
    end
end

% 恢复原文件（如果需要）
if exist(backup_file, 'file')
    fprintf('\n原文件备份保存在: %s\n', backup_file);
    fprintf('如需恢复原文件，运行: copyfile(''%s'', ''%s'')\n', backup_file, original_file);
end

fprintf('\n现在可以运行完整测试: test_3D_das_integration\n');
