function analyze_3D_acoustic_data(filename)
%% ========================================================================
%  三维声波测井数据分析和可视化脚本
%  功能：分析检波器和DAS数据，生成对比图表
%% ========================================================================

% 如果没有提供文件名，自动查找最新的数据文件
if nargin < 1
    files = dir('acoustic_logging_3D_*_with_DAS_results.mat');
    if isempty(files)
        error('未找到数据文件！请确保已运行主程序生成数据。');
    end
    % 按修改时间排序，选择最新的文件
    [~, idx] = max([files.datenum]);
    filename = files(idx).name;
    fprintf('自动选择最新数据文件: %s\n', filename);
end

% 加载数据
fprintf('正在加载数据文件: %s\n', filename);
load(filename);

% 检查数据完整性
if ~exist('receiver_data', 'var')
    error('数据文件中缺少检波器数据！');
end

fprintf('\n=== 数据文件信息 ===\n');
fprintf('检波器数量: %d\n', num_receivers);
fprintf('时间采样点数: %d\n', nt);
fprintf('采样时间长度: %.2f ms\n', max(time_axis)*1000);
fprintf('检波器间距: %.2f m\n', receiver_spacing);
fprintf('源距范围: %.2f - %.2f m\n', min(source_distances), max(source_distances));

if exist('enable_das', 'var') && enable_das && exist('das_data', 'var')
    fprintf('DAS系统: 启用\n');
    fprintf('DAS标距点数: %d\n', num_das_points);
    fprintf('标距长度: %.2f m\n', gauge_length);
    fprintf('标距重合比例: %.1f%%\n', gauge_overlap*100);
else
    fprintf('DAS系统: 未启用或数据缺失\n');
    enable_das = false;
end

%% ========================================================================
%  图1：检波器波形数据显示
%% ========================================================================

figure('Name', '检波器波形数据', 'Position', [100 100 1200 800]);

% 检波器数据归一化显示
subplot(2, 2, 1);
imagesc(time_axis*1e6, source_distances, receiver_data');
colorbar;
xlabel('时间 (μs)');
ylabel('源距 (m)');
title('检波器压力数据');
colormap(gca, 'seismic');

% 检波器波形叠加显示
subplot(2, 2, 2);
hold on;
for i = 1:min(8, num_receivers)  % 最多显示8道
    trace_data = receiver_data(:, i);
    % 归一化并偏移
    trace_norm = trace_data / max(abs(trace_data) + eps);
    plot(time_axis*1e6, trace_norm + i, 'LineWidth', 1);
end
xlabel('时间 (μs)');
ylabel('检波器道数 (偏移显示)');
title('检波器波形叠加显示');
grid on;

% 单道波形详细显示
subplot(2, 2, 3);
mid_receiver = ceil(num_receivers/2);
plot(time_axis*1e6, receiver_data(:, mid_receiver), 'b-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('压力 (Pa)');
title(sprintf('第%d个检波器波形 (源距: %.2fm)', mid_receiver, source_distances(mid_receiver)));
grid on;

% 频谱分析
subplot(2, 2, 4);
fs = 1/dt;  % 采样频率
freq = (0:nt/2-1) * fs / nt;
fft_data = abs(fft(receiver_data(:, mid_receiver)));
fft_data = fft_data(1:nt/2);
plot(freq/1000, 20*log10(fft_data/max(fft_data)), 'b-', 'LineWidth', 1.5);
xlabel('频率 (kHz)');
ylabel('幅度 (dB)');
title(sprintf('第%d个检波器频谱', mid_receiver));
grid on;
xlim([0, 20]);  % 显示0-20kHz

%% ========================================================================
%  图2：DAS数据显示（如果可用）
%% ========================================================================

if enable_das
    figure('Name', 'DAS应变率数据', 'Position', [150 150 1200 800]);
    
    % DAS数据归一化显示
    subplot(2, 2, 1);
    imagesc(time_axis*1e6, source_distances, das_data');
    colorbar;
    xlabel('时间 (μs)');
    ylabel('源距 (m)');
    title('DAS应变率数据');
    colormap(gca, 'seismic');
    
    % DAS波形叠加显示
    subplot(2, 2, 2);
    hold on;
    for i = 1:min(8, num_das_points)  % 最多显示8道
        trace_data = das_data(:, i);
        % 归一化并偏移
        trace_norm = trace_data / max(abs(trace_data) + eps);
        plot(time_axis*1e6, trace_norm + i, 'r-', 'LineWidth', 1);
    end
    xlabel('时间 (μs)');
    ylabel('DAS标距点 (偏移显示)');
    title('DAS应变率波形叠加显示');
    grid on;
    
    % 单道DAS波形详细显示
    subplot(2, 2, 3);
    plot(time_axis*1e6, das_data(:, mid_receiver), 'r-', 'LineWidth', 1.5);
    xlabel('时间 (μs)');
    ylabel('应变率 (1/s)');
    title(sprintf('第%d个DAS标距点波形 (源距: %.2fm)', mid_receiver, source_distances(mid_receiver)));
    grid on;
    
    % DAS频谱分析
    subplot(2, 2, 4);
    fft_das = abs(fft(das_data(:, mid_receiver)));
    fft_das = fft_das(1:nt/2);
    plot(freq/1000, 20*log10(fft_das/max(fft_das)), 'r-', 'LineWidth', 1.5);
    xlabel('频率 (kHz)');
    ylabel('幅度 (dB)');
    title(sprintf('第%d个DAS标距点频谱', mid_receiver));
    grid on;
    xlim([0, 20]);  % 显示0-20kHz
end

%% ========================================================================
%  图3：检波器与DAS对比分析（如果DAS可用）
%% ========================================================================

if enable_das
    figure('Name', '检波器与DAS对比分析', 'Position', [200 200 1400 900]);
    
    % 时域对比
    subplot(2, 3, 1);
    plot(time_axis*1e6, receiver_data(:, mid_receiver), 'b-', 'LineWidth', 1.5);
    hold on;
    % DAS数据归一化到检波器数据的量级
    das_norm = das_data(:, mid_receiver) * max(abs(receiver_data(:, mid_receiver))) / max(abs(das_data(:, mid_receiver)));
    plot(time_axis*1e6, das_norm, 'r--', 'LineWidth', 1.5);
    xlabel('时间 (μs)');
    ylabel('振幅');
    title(sprintf('第%d道时域对比', mid_receiver));
    legend('检波器', 'DAS (归一化)', 'Location', 'best');
    grid on;
    
    % 频域对比
    subplot(2, 3, 2);
    plot(freq/1000, 20*log10(fft_data/max(fft_data)), 'b-', 'LineWidth', 1.5);
    hold on;
    plot(freq/1000, 20*log10(fft_das/max(fft_das)), 'r--', 'LineWidth', 1.5);
    xlabel('频率 (kHz)');
    ylabel('幅度 (dB)');
    title(sprintf('第%d道频域对比', mid_receiver));
    legend('检波器', 'DAS', 'Location', 'best');
    grid on;
    xlim([0, 20]);
    
    % 相关性分析
    subplot(2, 3, 3);
    correlation = xcorr(receiver_data(:, mid_receiver), das_data(:, mid_receiver), 'normalized');
    lag_samples = -(length(correlation)-1)/2:(length(correlation)-1)/2;
    lag_time = lag_samples * dt * 1e6;  % 转换为微秒
    plot(lag_time, correlation, 'g-', 'LineWidth', 1.5);
    xlabel('时间延迟 (μs)');
    ylabel('归一化互相关');
    title(sprintf('第%d道互相关分析', mid_receiver));
    grid on;
    xlim([-50, 50]);  % 显示±50μs范围
    
    % 振幅对比（所有道）
    subplot(2, 3, 4);
    plot(source_distances, max_amplitudes_receiver, 'bo-', 'LineWidth', 1.5, 'MarkerSize', 6);
    hold on;
    plot(source_distances, max_amplitudes_das * max(max_amplitudes_receiver) / max(max_amplitudes_das), 'ro-', 'LineWidth', 1.5, 'MarkerSize', 6);
    xlabel('源距 (m)');
    ylabel('最大振幅');
    title('振幅随源距变化');
    legend('检波器', 'DAS (归一化)', 'Location', 'best');
    grid on;
    
    % 信噪比对比
    subplot(2, 3, 5);
    % 计算每道的信噪比（简单估计：最大值/均值）
    snr_receiver = max(abs(receiver_data), [], 1) ./ (mean(abs(receiver_data), 1) + eps);
    snr_das = max(abs(das_data), [], 1) ./ (mean(abs(das_data), 1) + eps);
    plot(source_distances, snr_receiver, 'bo-', 'LineWidth', 1.5, 'MarkerSize', 6);
    hold on;
    plot(source_distances, snr_das, 'ro-', 'LineWidth', 1.5, 'MarkerSize', 6);
    xlabel('源距 (m)');
    ylabel('信噪比估计');
    title('信噪比随源距变化');
    legend('检波器', 'DAS', 'Location', 'best');
    grid on;
    
    % 数据统计
    subplot(2, 3, 6);
    stats_data = [mean(max_amplitudes_receiver), std(max_amplitudes_receiver); ...
                  mean(max_amplitudes_das), std(max_amplitudes_das)];
    bar(stats_data);
    set(gca, 'XTickLabel', {'检波器', 'DAS'});
    ylabel('振幅统计');
    title('数据统计对比');
    legend('均值', '标准差', 'Location', 'best');
    grid on;
end

%% ========================================================================
%  数据质量报告
%% ========================================================================

fprintf('\n=== 数据质量分析报告 ===\n');
fprintf('检波器数据:\n');
fprintf('  最大振幅: %.2e\n', max(max_amplitudes_receiver));
fprintf('  最小振幅: %.2e\n', min(max_amplitudes_receiver));
fprintf('  平均振幅: %.2e\n', mean(max_amplitudes_receiver));
fprintf('  振幅标准差: %.2e\n', std(max_amplitudes_receiver));

if enable_das
    fprintf('DAS数据:\n');
    fprintf('  最大应变率: %.2e\n', max(max_amplitudes_das));
    fprintf('  最小应变率: %.2e\n', min(max_amplitudes_das));
    fprintf('  平均应变率: %.2e\n', mean(max_amplitudes_das));
    fprintf('  应变率标准差: %.2e\n', std(max_amplitudes_das));
    
    % 计算整体相关性
    overall_corr = corrcoef(receiver_data(:), das_data(:));
    fprintf('整体相关系数: %.3f\n', overall_corr(1,2));
end

fprintf('数值频散参数:\n');
if exist('lambda_min', 'var')
    fprintf('  最小波长: %.4f m\n', lambda_min);
    fprintf('  每波长网格点数: %.1f\n', points_per_wavelength);
    fprintf('  CFL数: %.2f\n', cfl);
    fprintf('  估计频散误差: %.2e\n', dispersion_error);
end

fprintf('\n分析完成！\n');

end
